#!/usr/bin/env python
# -*- coding: utf8 -*-
"""
Written by <PERSON>: https://github.com/chupman/
Example: Get guest info with folder and host placement

"""
import json
import requests

from samples.cloudsino_vcenter_sync_cmdb import esxi_arr_total


def main():
    # get_resp = requests.get(url="http://www.baidu.com")
    get_resp = requests.get(url="http://cmdb.inoah.com/zork-cmdb/objDesInner/searchAll")
    print(get_resp)


# 全量写入到cmdb
def post2cmdb(outputurl, add_server_url=None):
    # 添加宿主机信息到cmdb
    # add_server_url = outputurl + '/zork-cmdb/ObjectBase/create_inst'
    add_server_url = outputurl + '/zork-cmdb/ObjectBase/create_inst'
    new_headers = {
        'from': 'Y',
        'Content-Type': 'application/json'
    }
    new_data = {
        "zork_obj_id": "server",
        "zork_inst_name": "",
        "product_ip": "",
        "asset_status": 1,
        "create_user_name": "",
        "asset_model": "",
        "serial_number": "esxi['serialNumber']"
    }

    get = requests.get(url="http://cmdb.inoah.com/zork-cmdb/objDesInner/searchAll", headers=new_headers)
    # get = requests.get(url="http://www.baidu.com")
    print(get)

    res = requests.post(add_server_url, headers=new_headers, data=new_data)
    res = requests.post(url="http://cmdb.inoah.com/zork-cmdb/ObjectBase/create_inst", headers=new_headers, data={}, json={}, timeout=15, verify=False)
    print(res.text)

    for esxi in esxi_arr_total:
        new_data = {
            "zork_obj_id": "server",
            "zork_inst_name": "",
            "product_ip": "",
            "asset_status": 1,
            "create_user_name": "",
            "asset_model": "",
            "serial_number": "esxi['serialNumber']"
        }
        print ("esxi:"+esxi)
        # res = requests.post(add_server_url, headers=new_headers, data=new_data)
        res = requests.post(url=add_server_url, headers=new_headers, data=new_data, json={}, timeout=15, verify=False)
        print(res.text)

    # 添加虚拟机信息到cmdb

    # 建立关系 宿主机的hostname


# Start program
if __name__ == "__main__":
    # main()
    post2cmdb("http://cmdb.inoah.com/","http://cmdb.inoah.com/")