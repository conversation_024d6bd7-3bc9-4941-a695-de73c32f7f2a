#!/bin/bash

# VMware vSphere Python SDK Community Samples Docker Entrypoint
# This script provides a flexible way to run various sample scripts

set -e

# Function to display usage
show_usage() {
    echo "Usage: docker run [OPTIONS] pyvmomi-samples [COMMAND] [ARGS...]"
    echo ""
    echo "Commands:"
    echo "  bash                    Start interactive bash shell"
    echo "  python <script.py>      Run a specific Python script"
    echo "  hello                   Run hello_world_vcenter.py example"
    echo "  list-vms                Run getallvms.py example"
    echo "  help                    Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  VCENTER_HOST           vCenter server hostname/IP"
    echo "  VCENTER_USER           vCenter username"
    echo "  VCENTER_PASSWORD       vCenter password"
    echo "  VCENTER_PORT           vCenter port (default: 443)"
    echo "  DISABLE_SSL_VERIFY     Disable SSL verification (default: true)"
    echo ""
    echo "Examples:"
    echo "  # Interactive shell"
    echo "  docker run -it pyvmomi-samples bash"
    echo ""
    echo "  # Run hello world example"
    echo "  docker run -e VCENTER_HOST=vcenter.example.com \\"
    echo "             -e VCENTER_USER=admin \\"
    echo "             -e VCENTER_PASSWORD=password \\"
    echo "             pyvmomi-samples hello"
    echo ""
    echo "  # Run custom script"
    echo "  docker run -v /path/to/script.py:/app/samples/custom_script.py \\"
    echo "             pyvmomi-samples python custom_script.py"
}

# Function to set up vCenter connection parameters
setup_vcenter_args() {
    VCENTER_ARGS=""
    
    if [ -n "$VCENTER_HOST" ]; then
        VCENTER_ARGS="$VCENTER_ARGS -s $VCENTER_HOST"
    fi
    
    if [ -n "$VCENTER_USER" ]; then
        VCENTER_ARGS="$VCENTER_ARGS -u $VCENTER_USER"
    fi
    
    if [ -n "$VCENTER_PASSWORD" ]; then
        VCENTER_ARGS="$VCENTER_ARGS -p $VCENTER_PASSWORD"
    fi
    
    if [ -n "$VCENTER_PORT" ]; then
        VCENTER_ARGS="$VCENTER_ARGS -o $VCENTER_PORT"
    fi
    
    if [ "${DISABLE_SSL_VERIFY:-true}" = "true" ]; then
        VCENTER_ARGS="$VCENTER_ARGS --disable-ssl-verification"
    fi
}

# Main execution logic
case "$1" in
    "help"|"--help"|"-h")
        show_usage
        exit 0
        ;;
    "hello")
        setup_vcenter_args
        echo "Running hello_world_vcenter.py..."
        exec python hello_world_vcenter.py $VCENTER_ARGS
        ;;
    "list-vms")
        setup_vcenter_args
        echo "Running getallvms.py..."
        exec python getallvms.py $VCENTER_ARGS
        ;;
    "python")
        shift
        echo "Running Python script: $@"
        exec python "$@"
        ;;
    "bash"|"sh")
        echo "Starting interactive shell..."
        exec bash
        ;;
    "")
        echo "Starting interactive shell..."
        exec bash
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use 'help' to see available commands."
        show_usage
        exit 1
        ;;
esac
