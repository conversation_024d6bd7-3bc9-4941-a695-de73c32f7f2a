[tox]
envlist = py38,py39,py310,py311,flake8,pycodestyle
skip_missing_interpreters = true

[testenv]
deps = -r{toxinidir}/test-requirements.txt
commands = pytest {posargs}

[testenv:flake8]
deps = flake8
commands = flake8 samples/

[testenv:pycodestyle]
deps = pycodestyle
commands = pycodestyle samples/

[flake8]
max-line-length = 100
exclude = .git,__pycache__,build,dist,*.egg-info,venv,new_env
ignore = E203,W503

[pycodestyle]
max-line-length = 100
exclude = .git,__pycache__,build,dist,*.egg-info,venv,new_env
