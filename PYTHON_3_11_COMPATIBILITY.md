# Python 3.11.9 兼容性更新

本文档记录了为使 pyvmomi-community-samples 项目兼容 Python 3.11.9 所做的更改。

## 主要更改

### 1. 依赖项更新 (requirements.txt)

**更改前:**
```
argparse
pyvmomi>=v6.5.0.2017.5-1
requests
suds>=0.4,<0.7 ; python_version < '3'
suds-jurko ; python_version >= '3.0'
vcrpy>=1.1.1
pyinstaller
requests
```

**更改后:**
```
# pyvmomi - VMware vSphere API Python bindings
pyvmomi>=7.0.3

# HTTP library
requests>=2.25.1

# SOAP client library - conditional based on Python version
suds>=0.4,<0.7 ; python_version < '3'
suds-jurko>=0.6 ; python_version >= '3.0' and python_version < '3.11'
suds-community>=1.1.0 ; python_version >= '3.11'

# VCR.py for HTTP interaction recording/playback
vcrpy>=4.1.1

# PyInstaller for creating executables
pyinstaller>=4.5.1
```

**主要变化:**
- 移除了 `argparse`（Python 3.2+ 内置）
- 更新 `pyvmomi` 到支持 Python 3.11 的版本
- 为 Python 3.11 添加了 `suds-community` 支持
- 移除重复的 `requests` 条目
- 更新所有依赖项到兼容的版本

### 2. Python 版本支持更新

#### .travis.yml
**更改前:**
```yaml
python:
    - "2.7"
    - "3.6"
    - "pypy"
```

**更改后:**
```yaml
python:
    - "3.8"
    - "3.9"
    - "3.10"
    - "3.11"
    - "pypy3"
```

#### setup.py
添加了 Python 版本分类器和最低版本要求：
```python
classifiers=[
    # ... 其他分类器 ...
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    # ... 其他分类器 ...
],
python_requires='>=3.8',
```

### 3. 代码兼容性修复

#### samples/pyvmomi-to-suds.py
修复了 `cookielib` 导入问题：
```python
try:
    # Python 2
    import cookielib
except ImportError:
    # Python 3
    import http.cookiejar as cookielib
```

#### samples/getorphanedvms.py
修复了两个主要兼容性问题：

1. **unicode 类型检查:**
```python
def url_fix(url_str, charset='utf-8'):
    # Python 3 compatibility: unicode type doesn't exist in Python 3
    try:
        # Python 2
        if isinstance(url_str, unicode):
            url_str = url_str.encode(charset, 'ignore')
    except NameError:
        # Python 3: unicode doesn't exist, str is already unicode
        pass
    # ... 其余代码 ...
```

2. **base64 编码方法:**
```python
# Python 3 compatibility: use encodebytes instead of encodestring
try:
    # Python 3
    base64string = base64.encodebytes(
        ('%s:%s' % (username, password)).encode('utf-8')).decode('ascii').replace('\n', '')
except AttributeError:
    # Python 2 fallback
    base64string = base64.encodestring(
        '%s:%s' % (username, password)).replace('\n', '')
```

### 4. 测试依赖更新 (test-requirements.txt)

**更改前:**
```
mock>=1.0
testtools>=0.9.34
```

**更改后:**
```
# Testing dependencies
# Note: mock is part of unittest.mock in Python 3.3+, but keeping for compatibility
mock>=4.0.3
testtools>=2.5.0
pytest>=6.2.5
pytest-cov>=3.0.0
```

## 兼容性说明

### 支持的 Python 版本
- **最低要求:** Python 3.8
- **推荐版本:** Python 3.11.9
- **测试版本:** Python 3.8, 3.9, 3.10, 3.11

### 依赖项兼容性
- **pyvmomi:** 使用 7.0.3+ 版本以确保 Python 3.11 兼容性
- **suds:** 根据 Python 版本自动选择合适的 SOAP 客户端库
  - Python < 3: `suds`
  - Python 3.0-3.10: `suds-jurko`
  - Python 3.11+: `suds-community`

### 已知问题和解决方案

1. **cookielib 模块重命名**
   - 问题: Python 3 中 `cookielib` 被重命名为 `http.cookiejar`
   - 解决: 使用 try/except 导入兼容性代码

2. **unicode 类型不存在**
   - 问题: Python 3 中没有 `unicode` 类型
   - 解决: 使用 try/except 检查类型存在性

3. **base64.encodestring 弃用**
   - 问题: Python 3 中 `encodestring` 被弃用，应使用 `encodebytes`
   - 解决: 优先使用新方法，失败时回退到旧方法

## 安装和使用

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行测试
```bash
pip install -r test-requirements.txt
pytest
```

### 验证兼容性
```bash
python -c "import sys; print(f'Python {sys.version}')"
python -c "import pyvmomi; print('pyvmomi imported successfully')"
```

## 迁移指南

如果您正在从 Python 2.7 或较旧的 Python 3 版本迁移：

1. **更新 Python 版本** 到 3.8 或更高版本
2. **重新安装依赖项** 使用更新后的 requirements.txt
3. **测试您的代码** 确保没有其他兼容性问题
4. **更新 CI/CD 配置** 使用支持的 Python 版本

## 贡献

如果您发现其他兼容性问题，请：
1. 创建 issue 描述问题
2. 提供复现步骤
3. 如果可能，提供修复建议或 PR
