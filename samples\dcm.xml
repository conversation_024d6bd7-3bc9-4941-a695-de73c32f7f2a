<application xmlns="http://wadl.dev.java.net/2009/02" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <grammars>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="unqualified">
            <xs:element name="Message" type="messages"/>
            <xs:element name="OOBServer" type="oobServer"/>
            <xs:complexType name="messages">
                <xs:sequence>
                    <xs:element minOccurs="0" name="data" type="xs:string"/>
                    <xs:element minOccurs="0" name="reason" type="xs:string"/>
                    <xs:element minOccurs="0" name="status" type="xs:string"/>
                    <xs:element minOccurs="0" name="type" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="oobServer">
                <xs:sequence>
                    <xs:element minOccurs="0" name="appName" type="xs:string"/>
                    <xs:element minOccurs="0" name="businessName" type="xs:string"/>
                    <xs:element minOccurs="0" name="chargeBy" type="xs:string"/>
                    <xs:element minOccurs="0" name="dataCenter" type="xs:string"/>
                    <xs:element minOccurs="0" name="description" type="xs:string"/>
                    <xs:element minOccurs="0" name="endU" type="xs:string"/>
                    <xs:element minOccurs="0" name="frame" type="xs:string"/>
                    <xs:element minOccurs="0" name="manufacturer" type="xs:string"/>
                    <xs:element minOccurs="0" name="model" type="xs:string"/>
                    <xs:element minOccurs="0" name="moreChargeBy" type="xs:string"/>
                    <xs:element minOccurs="0" name="name" type="xs:string"/>
                    <xs:element minOccurs="0" name="oobIP" type="xs:string"/>
                    <xs:element minOccurs="0" name="oobPassword" type="xs:string"/>
                    <xs:element minOccurs="0" name="oobUser" type="xs:string"/>
                    <xs:element minOccurs="0" name="osName" type="xs:string"/>
                    <xs:element minOccurs="0" name="osVersion" type="xs:string"/>
                    <xs:element minOccurs="0" name="productIP" type="xs:string"/>
                    <xs:element minOccurs="0" name="raidLevel" type="xs:string"/>
                    <xs:element minOccurs="0" name="rfid" type="xs:string"/>
                    <xs:element minOccurs="0" name="room" type="xs:string"/>
                    <xs:element minOccurs="0" name="serviceTag" type="xs:string"/>
                    <xs:element minOccurs="0" name="startU" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
    </grammars>
    <resources base="http://www.dodocloud.cn/ws/restful">
        <resource path="/dcm">
            <resource path="/monitor/getMonitorData">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/getmonitor">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/getDeviceSerialNumber">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/getPersonalWorkBench">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/getallservicetag">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/biz/addBiz">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/biz/addDevice">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/biz/addPath">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/biz/delete">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/biz/getAllBiz">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/biz/getSubsByParentId">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/biz/updateBiz">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/biz/updatePath">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/monitor/getPowerstatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/getdevicepowerstate">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/getAssetsByPage">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/getBaseInfoByPage">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/getChangeInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/getRoomInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/roomdata">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/deviceinfobypage">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/getBaseInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/getdeviceinfo">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/framedata">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/getFrameInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/zbgl/deletezbglbydate">
                <method name="POST">
                    <request>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="token" style="query" type="xs:string"/>
                            <param name="zbgl_date" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/zbgl/getnickname">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/zbgl/importzbgl">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/zbgl/savezbgl">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
                <method name="POST">
                    <request>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="token" style="query" type="xs:string"/>
                            <param name="begindate" style="query" type="xs:string"/>
                            <param name="name" style="query" type="xs:string"/>
                            <param name="phonenum" style="query" type="xs:string"/>
                            <param name="mail" style="query" type="xs:string"/>
                            <param name="leader" style="query" type="xs:string"/>
                            <param name="leaderphonenum" style="query" type="xs:string"/>
                            <param name="leadermail" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/zbgl/shiftzbgl">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
                <method name="POST">
                    <request>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="token" style="query" type="xs:string"/>
                            <param name="begindate1" style="query" type="xs:string"/>
                            <param name="begindate2" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/monitor/getmonitorhistory">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/zbgl/savezbgl">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
                <method name="POST">
                    <request>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="token" style="query" type="xs:string"/>
                            <param name="begindate" style="query" type="xs:string"/>
                            <param name="name" style="query" type="xs:string"/>
                            <param name="phonenum" style="query" type="xs:string"/>
                            <param name="mail" style="query" type="xs:string"/>
                            <param name="leader" style="query" type="xs:string"/>
                            <param name="leaderphonenum" style="query" type="xs:string"/>
                            <param name="leadermail" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/zbgl/shiftzbgl">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
                <method name="POST">
                    <request>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="token" style="query" type="xs:string"/>
                            <param name="begindate1" style="query" type="xs:string"/>
                            <param name="begindate2" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/monitor/getDeviceStatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/getKvmUrl">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/getTemporaryPassword">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/getdevicemonitorstatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/updateOobUser">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/consumption/getroomhistoryconsumption">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/consumption/deviceconsumption">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/consumption/devicecurrentconsumption">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/consumption/getCurrentEnergyConsumption">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/consumption/getDeviceEnergyConsumption">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                        <representation mediaType="application/x-www-form-urlencoded">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/alarm/degradelevel">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/alarm/getalarms">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/alarm/handlingAlarm">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/alarm/processalarm">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/OOB">
            <resource path="/closeAlarm/{accessToken}">
                <param name="accessToken" style="template" type="xs:string"/>
                <method name="GET">
                    <request>
                        <param name="alarmID" style="query" type="xs:string"/>
                        <param name="isStateFlag" style="query" type="xs:string"/>
                        <param name="closeType" style="query" type="xs:string"/>
                        <param name="maintenancePeriod" style="query" type="xs:string"/>
                        <param name="remark" style="query" type="xs:string"/>
                    </request>
                    <response>
                        <representation mediaType="application/json;charset=UTF-8"/>
                    </response>
                </method>
            </resource>
            <resource path="/getAssetSNInfo/{accessToken}">
                <param name="accessToken" style="template" type="xs:string"/>
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="*/*">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/onlineVerification/{accessToken}">
                <param name="accessToken" style="template" type="xs:string"/>
                <method name="POST">
                    <request>
                        <representation mediaType="application/json"/>
                    </request>
                    <response>
                        <representation mediaType="application/json"/>
                    </response>
                </method>
            </resource>
            <resource path="/setOfflineAndDelete/{accessToken}">
                <param name="accessToken" style="template" type="xs:string"/>
                <method name="POST">
                    <request>
                        <representation mediaType="application/json"/>
                    </request>
                    <response>
                        <representation mediaType="application/json"/>
                    </response>
                </method>
            </resource>
            <resource path="/setOfflineState/{accessToken}">
                <param name="accessToken" style="template" type="xs:string"/>
                <method name="POST">
                    <request>
                        <representation mediaType="application/json"/>
                    </request>
                    <response>
                        <representation mediaType="application/json"/>
                    </response>
                </method>
            </resource>
            <resource path="/setOnlineState/{accessToken}">
                <param name="accessToken" style="template" type="xs:string"/>
                <method name="POST">
                    <request>
                        <representation mediaType="application/json"/>
                    </request>
                    <response>
                        <representation mediaType="application/json"/>
                    </response>
                </method>
            </resource>
            <resource path="/updateAssetInfo/{accessToken}">
                <param name="accessToken" style="template" type="xs:string"/>
                <method name="POST">
                    <request>
                        <representation mediaType="application/json"/>
                    </request>
                    <response>
                        <representation mediaType="*/*"/>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/importAssets">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/importDeviceInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/importOppoAsset">
                <method name="POST">
                    <request>
                        <representation mediaType="*/*">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/monitor/addMonitoring">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/addmonitor">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/addmonitor/addperf">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/addmonitor/delperf">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/currentAlarm/getAlarmList">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getAlarmThreshold">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getCurrentAlarm">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getDeviceEventLog">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getHistoryAlarm">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getHistoryAlarmList">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/updateAssetStatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/asset/updatedevicestatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/importroom">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/getPXEHardwareInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/importframe">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/importspareinout">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/editsparein">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/queryspareinfo">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/monitor/getDistributedNode">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/getmac">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/getDevicePassword">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/control/devicecontrol">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/control/resetlog">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/asset/synchroInstallStatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/user/synchruser">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/synch/abcassetaccept">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="name" style="query" type="xs:string"/>
                            <param name="dataCenter" style="query" type="xs:string"/>
                            <param name="operateType" style="query" type="xs:int"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/synch/acceptCommand">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/synch/assetaccept">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="name" style="query" type="xs:string"/>
                            <param name="dataCenter" style="query" type="xs:string"/>
                            <param name="operateType" style="query" type="xs:int"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/synch/refreshassetaccept">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="dataCenter" style="query" type="xs:string"/>
                            <param name="deviceids" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/synch/simpleassetaccept">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="name" style="query" type="xs:string"/>
                            <param name="dataCenter" style="query" type="xs:string"/>
                            <param name="operateType" style="query" type="xs:int"/>
                            <param name="deviceids" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dianzi10suo">
            <resource path="/currentAlarm/resourceType">
                <method name="GET">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/logo">
            <resource path="/system/updateLogoImage">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="fileName" style="query" type="xs:string"/>
                            <param name="logoUrl" style="query" type="xs:string"/>
                            <param name="filePath" style="query" type="xs:string"/>
                            <param name="absolutePath" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dianzi10suo">
            <resource path="/currentAlarm/inspectDevice">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dianzi10suo">
            <resource path="/currentAlarm/knowledgeType">
                <method name="GET">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/user/getdbAddress">
                <method name="POST">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/user/syncNodeUserInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/user/validate">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dianzi10suo">
            <resource path="/currentAlarm/getCurrentAlarmCategory">
                <method name="GET">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dianzi10suo">
            <resource path="/currentAlarm/getHealthStatus">
                <method name="GET">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dianzi10suo">
            <resource path="/currentAlarm/getAlarmDeviceTypeTotal">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getAlarmStatistics">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getAlarmTotal">
                <method name="GET">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getAlarmTotalTen">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getAlarmTrendDaily">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/currentAlarm/getHomeAlarmInfo">
                <method name="GET">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/notice/emailSend">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/notice/smsSend">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/synch/syncNodeAllUserInfoTimer">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="fileName" style="query" type="xs:string"/>
                            <param name="headquartersHostIp" style="query" type="xs:string"/>
                            <param name="timeMillis" style="query" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/license/getLicInfo">
                <method name="GET">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/license/getLicneseInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/license/postLicense">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="token" style="query" type="xs:string"/>
                            <param name="serverId" style="query" type="xs:int"/>
                            <param name="ipaddress" style="query" type="xs:string"/>
                            <param name="port" style="query" type="xs:int"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/license/postLicenseZip">
                <method name="POST">
                    <request>
                        <representation mediaType="multipart/form-data">
                            <param name="password" style="query" type="xs:string"/>
                            <param name="token" style="query" type="xs:string"/>
                            <param name="serverId" style="query" type="xs:int"/>
                            <param name="ipaddress" style="query" type="xs:string"/>
                            <param name="port" style="query" type="xs:int"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/sad/config">
            <resource path="/addCheckInfo">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/downloadDriver/{id}">
                <param name="id" style="template" type="xs:string"/>
                <method name="GET">
                    <request/>
                    <response status="204"/>
                </method>
            </resource>
            <resource path="/nodelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/strategylist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/sad/asset">
            <resource path="/addDeviceFromAsset">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/devicelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/ipxe/bmcUserList">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/ipxe/listBmcManufConfig">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/ipxe/saveBmcManufConfig">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/queryPowerStatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/turnToWait">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/updatePowerStatus">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/sad/template">
            <resource path="/businessipTemplatelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/diskTemplatelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/osTemplatelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/preScriptTemplatelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/raidTemplatelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/scriptTemplatelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/softwareTemplatelist">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/sad/install">
            <resource path="/queryInstallProgress">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/runInstall">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/sad/install">
            <resource path="/citicbank/installByMac">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/installByMac">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/accept/acceptAsset">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/node/toolCheck">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/repair/changeConfirm">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/repair/closeWorkOrder">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/monitor/getOobPwd">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/monitor/updateOobPwd">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/openapi/boc-servers">
                <method name="GET">
                    <request>
                        <param name="X-AUTH-TOKEN" style="header" type="xs:string"/>
                        <param name="start" style="query" type="xs:int"/>
                        <param name="size" style="query" type="xs:int"/>
                        <param name="orderBy" style="query" type="xs:string"/>
                        <param name="desc" style="query" type="xs:boolean"/>
                        <param name="deviceid" style="query" type="xs:string"/>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/openapi/boc-servers-components">
                <method name="GET">
                    <request>
                        <param name="X-AUTH-TOKEN" style="header" type="xs:string"/>
                        <param name="start" style="query" type="xs:int"/>
                        <param name="size" style="query" type="xs:int"/>
                        <param name="deviceid" style="query" type="xs:string"/>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/openapi/boc-servers-status">
                <method name="GET">
                    <request>
                        <param name="X-AUTH-TOKEN" style="header" type="xs:string"/>
                        <param name="start" style="query" type="xs:int"/>
                        <param name="size" style="query" type="xs:int"/>
                        <param name="deviceid" style="query" type="xs:string"/>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/dcm">
            <resource path="/report/getMonthReport">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
        <resource path="/grafana">
            <method name="GET">
                <response>
                    <representation mediaType="application/json">
                        <param name="result" style="plain" type="xs:string"/>
                    </representation>
                </response>
            </method>
            <resource path="/annotations">
                <method name="POST">
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/query">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
            <resource path="/search">
                <method name="POST">
                    <request>
                        <representation mediaType="application/json">
                            <param name="request" style="plain" type="xs:string"/>
                        </representation>
                    </request>
                    <response>
                        <representation mediaType="application/json">
                            <param name="result" style="plain" type="xs:string"/>
                        </representation>
                    </response>
                </method>
            </resource>
        </resource>
    </resources>
</application>
