# pyvmomi - VMware vSphere API Python bindings
pyvmomi>=7.0.3

# HTTP library
requests>=2.25.1

# SOAP client library - conditional based on Python version
suds>=0.4,<0.7 ; python_version < '3'
suds-jurko>=0.6 ; python_version >= '3.0' and python_version < '3.11'
suds-community>=1.1.0 ; python_version >= '3.11'

# VCR.py for HTTP interaction recording/playback
vcrpy>=4.1.1

# PyInstaller for creating executables
pyinstaller>=4.5.1
