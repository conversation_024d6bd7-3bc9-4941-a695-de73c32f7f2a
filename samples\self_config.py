#coding=utf-8 
# falcon
endpoint = "vcenter" # 上报给 open-falcon 的 endpoint
push_api = "http://127.0.0.1:1988/v1/push" # 上报的 http api 接口
push_api2 = "http://127.0.0.1:1988/v1/push" # 上报的 http api 接口
interval = 60 # 上报的 step 间隔

# vcenter
host = "*************" # vcenter 的地址
user = "<EMAIL>" # vcenter 的用户名
password = "Zorkdata.8888" # vcenter 的密码
port = 443 # vcenter 的端口
disable_ssl_verification = True

silent = False
out_put_json = False
jsonfile = "vms.json"


add_to_cmdb = True
# cmdb
# cmdb_host = "http://cmdb.shandong2.com"
cmdb_host = "http://cmdb.inoah.com"
cmdb_add_inst_url = cmdb_host + '/zork-cmdb/ObjectBase/create_inst'
cmdb_add_host_url = cmdb_host + '/zork-cmdb/host/add_host_to_resource'
cmdb_add_inst_asst_url = cmdb_host + '/zork-cmdb/ObjectBase/set_inst_asst'

# cloudsino
cloudsino_host = "https://www.dodocloud.cn:8413/"
cloudsino_get_device_url = cloudsino_host + "ws/restful/dcm/asset/getdeviceinfo"
cloudsino_token = ""

# esxi
esxi_names = [] # 需要采集的 esxi ，留空则全部采集
# datastore
datastore_names = [] # 需要采集的 datastore ，留空则全部采集 
# vm
vm_enable = True # 是否要采集虚拟机信息
vm_names = [] # 需要采集的虚拟机，留空则全部采集
