Executable Samples
==================

This directory contains a collection of executable sample
scripts that use the pyVmomi library. There is a `tools`
directory that holds a collection of tools and a `tests`
directory that holds the tests for those tools.

Quality and License
===================
Scripts are provided as-is by numerous contributors and
the status of any sample at any point is subject to change.
The project is intended as a collaborative exercise in
community learning and may not contain best practice methods.

All samples revert to the license of the project and all
ownership reverts to the community project. 

Contribution Notes
==================
* If a script is in this directory, it is an executable sample.
* conform to pep8
* avoid using any special tools beyond pyvmomi.
* do not extend the pyVmomi API in this project we have two separate
  projects dedicated to that.
* tests are appreciated but optional
  because of this sample quality must be manually assessed
  bug reports and fixes are much appreciated.
* A reviewer will pull a new sample for testing and will attempt to
  run the sample. If the reviewer can do this, the sample can be merged

Getting Help
============

* Question can be opened as issues at 
  https://github.com/vmware/pyvmomi-community-samples/issues
* The IRC forum [#pyvmomi](http://webchat.freenode.net/?channels=#pyvmomi)
  is for developers working _with_ pyVmomi
