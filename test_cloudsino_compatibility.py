#!/usr/bin/env python
"""
测试 cloudsino_vcenter_sync_cmdb.py 文件的 Python 3.11.9 兼容性

此脚本验证：
1. 语法正确性
2. 导入兼容性
3. 基本功能测试
"""

import sys
import ast
import traceback
import importlib.util

def test_syntax():
    """测试 Python 语法正确性"""
    print("测试 cloudsino_vcenter_sync_cmdb.py 语法...")
    
    try:
        with open('samples/cloudsino_vcenter_sync_cmdb.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 编译源代码检查语法
        ast.parse(source_code)
        print("✅ 语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行 {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 文件读取错误: {e}")
        return False

def test_imports():
    """测试导入兼容性"""
    print("\n测试导入兼容性...")
    
    # 模拟导入测试
    required_modules = [
        'json',
        'requests',
        'collections',
        'pyVmomi',
        'pyVim.connect',
        'pyVim.task',
        'pyVmomi.vmodl',
    ]
    
    optional_modules = [
        'samples.tools.cli',
        'samples.tools.service_instance',
        'samples.self_config',
    ]
    
    success_count = 0
    total_count = len(required_modules)
    
    for module in required_modules:
        try:
            if '.' in module:
                # 处理子模块导入
                parts = module.split('.')
                parent = __import__(parts[0])
                for part in parts[1:]:
                    parent = getattr(parent, part)
            else:
                __import__(module)
            print(f"✅ {module} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module} 导入失败: {e}")
        except Exception as e:
            print(f"⚠️  {module} 导入异常: {e}")
    
    print(f"\n必需模块导入结果: {success_count}/{total_count}")
    
    # 测试可选模块
    print("\n测试可选模块:")
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} 导入成功")
        except ImportError as e:
            print(f"⚠️  {module} 导入失败 (可选): {e}")
    
    return success_count == total_count

def test_python3_features():
    """测试 Python 3 特性使用"""
    print("\n测试 Python 3 特性...")
    
    try:
        with open('samples/cloudsino_vcenter_sync_cmdb.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用了 Python 3 的 items() 而不是 iteritems()
        if '.iteritems()' in content:
            print("❌ 发现 Python 2 风格的 .iteritems() 调用")
            return False
        else:
            print("✅ 使用了 Python 3 风格的 .items() 调用")
        
        # 检查是否使用了 Python 3 的 print() 函数
        import re
        print_pattern = r'print\s+[^(]'  # print 后面跟空格但不跟括号
        if re.search(print_pattern, content):
            print("❌ 发现 Python 2 风格的 print 语句")
            return False
        else:
            print("✅ 使用了 Python 3 风格的 print() 函数")
        
        return True
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_string_handling():
    """测试字符串处理兼容性"""
    print("\n测试字符串处理...")
    
    try:
        # 测试 JSON 处理
        import json
        test_data = {"test": "测试", "number": 123}
        json_str = json.dumps(test_data, ensure_ascii=False)
        parsed = json.loads(json_str)
        print("✅ JSON 处理正常")
        
        # 测试字符串格式化
        test_format = "测试: %s, 数字: %d" % ("value", 42)
        print("✅ 字符串格式化正常")
        
        return True
    except Exception as e:
        print(f"❌ 字符串处理测试失败: {e}")
        return False

def test_dictionary_operations():
    """测试字典操作兼容性"""
    print("\n测试字典操作...")
    
    try:
        # 模拟代码中的字典操作
        test_dict = {
            'datacenter1': {
                'cluster1': {
                    'host1': {
                        'vm1': {'cpu': 2, 'mem': 4096}
                    }
                }
            }
        }
        
        # 测试嵌套字典遍历 (类似代码中的四层循环)
        for key1, value1 in test_dict.items():
            for key2, value2 in value1.items():
                for key3, value3 in value2.items():
                    for key4, value4 in value3.items():
                        assert isinstance(value4, dict)
        
        print("✅ 字典操作兼容性正常")
        return True
    except Exception as e:
        print(f"❌ 字典操作测试失败: {e}")
        return False

def test_http_requests():
    """测试 HTTP 请求兼容性"""
    print("\n测试 HTTP 请求兼容性...")
    
    try:
        import requests
        import json
        
        # 测试 JSON 序列化 (模拟代码中的用法)
        test_data = {
            "zork_obj_id": "server",
            "zork_inst_name": "test",
            "product_ip": "***********"
        }
        
        json_data = json.dumps(test_data)
        headers = {
            'from': 'Y',
            'Content-Type': 'application/json'
        }
        
        print("✅ HTTP 请求数据准备正常")
        print("✅ JSON 序列化正常")
        return True
    except Exception as e:
        print(f"❌ HTTP 请求测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("cloudsino_vcenter_sync_cmdb.py Python 3.11.9 兼容性测试")
    print("=" * 60)
    
    tests = [
        ("语法检查", test_syntax),
        ("导入兼容性", test_imports),
        ("Python 3 特性", test_python3_features),
        ("字符串处理", test_string_handling),
        ("字典操作", test_dictionary_operations),
        ("HTTP 请求", test_http_requests),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 cloudsino_vcenter_sync_cmdb.py 兼容性测试全部通过!")
        print("\n✅ 文件已成功修复为 Python 3.11.9 兼容")
        return 0
    else:
        print("⚠️  部分测试失败，请检查兼容性问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
