#!/usr/bin/env python
# -*- coding: utf8 -*-

"""
获取所有的vcenter相关信息
包括Esxi的硬件资源信息和vmware客户端的硬件分配信息
"""
import atexit
from pyVim.connect import Disconnect, SmartConnectNoSSL
from pyVmomi import vim
import json
import sys

HOST = sys.argv[1]
USERNAME = sys.argv[2]
PASSWORD = sys.argv[3]


def collect(ip='localhost', port=443, username='root', password=''):
    si = SmartConnectNoSSL(
        host=ip,
        user=username,
        pwd=password,
        port=port)
    atexit.register(Disconnect, si)
    content = si.RetrieveContent()
    container = content.viewManager.CreateContainerView(content.rootFolder, [vim.HostSystem], True)
    esxi_obj = [view for view in container.view]
    esxi_arr_total = []
    for esxi in esxi_obj:
        esxi_arr = []
        esxi_host_collect_result = {
            "code": "000000",
            "type": "host",
            "appsystem": "",
            "modulename": "host",
        }
        esxi_host_summary_info = {
            "CPU": esxi.summary.hardware.cpuModel,
            "CPUNum": esxi.summary.hardware.numCpuPkgs,
            "CPUNumberOfCores": esxi.summary.hardware.numCpuCores,
            "CPUThreadNum": esxi.summary.hardware.numCpuThreads,
            "memory": int(esxi.summary.hardware.memorySize / 1024 / 1024 / 1024),
            "memUsed": int(esxi.summary.quickStats.overallMemoryUsage / 1024),
            "memFree": int(esxi.summary.hardware.memorySize / 1024 / 1024 / 1024 - (esxi.summary.quickStats.overallMemoryUsage / 1024)),
            "ip": ip,
            "hostname": esxi.name,
            "os": esxi.summary.config.product.fullName,
            "osType": "Esxi",
            "osBit": "64",
            "serialNumber": "",
            "diskSize": "",
            "computermodel": esxi.summary.hardware.model
        }
        for i in esxi.summary.hardware.otherIdentifyingInfo:
            if isinstance(i, vim.host.SystemIdentificationInfo) and i.identifierType.key == "SerialNumberTag":
                esxi_host_summary_info["serialNumber"] = i.identifierValue
        disk_size = 0
        for ds in esxi.datastore:
            disk_size += int(ds.summary.capacity / 1024 / 1024 / 1024)
        esxi_host_summary_info["diskSize"] = disk_size

        esxi_arr.append(esxi_host_summary_info)

        esxi_host_collect_result["ip"] = ip
        esxi_host_collect_result["hostname"] = esxi.name
        esxi_host_collect_result["data"] = esxi_arr

        esxi_arr_total.append(esxi_host_collect_result)
    print "#%s#" % json.dumps(esxi_arr_total)


if __name__ == '__main__':
    collect(HOST, 443, USERNAME, PASSWORD)
