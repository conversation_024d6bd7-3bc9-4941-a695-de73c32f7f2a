# Git files
.git
.gitignore
.gitattributes

# Python cache and build files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
new_env/
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker files (avoid recursion)
samples/docker_file/

# Test and coverage files
.tox/
.coverage
.pytest_cache/
htmlcov/

# Documentation
docs/
*.md
!README.md

# Log files
*.log

# Temporary files
*.tmp
*.temp

# Local configuration files that shouldn't be in container
local_config.py
.env
.env.local

# Output files
*.json
*.xml
output/
results/

# Compiled files
*.pyc
*.pyo
