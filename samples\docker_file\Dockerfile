# VMware vSphere Python SDK Community Samples Docker Image
# Based on Python 3.11 for compatibility
FROM python:3.11-slim

# Set metadata
LABEL maintainer="pyvmomi-community"
LABEL description="VMware vSphere Python SDK Community Samples"
LABEL version="1.0"

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app/samples

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    curl \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
COPY test-requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r test-requirements.txt

# Copy the entire project
COPY . .

# Install the package in development mode
RUN pip install -e .

# Create a non-root user
RUN useradd -m -u 1000 pyvmomi && \
    chown -R pyvmomi:pyvmomi /app

# Switch to non-root user
USER pyvmomi

# Set working directory to samples
WORKDIR /app/samples

# Copy entrypoint script
COPY docker_file/entrypoint.sh /entrypoint.sh
USER root
RUN chmod +x /entrypoint.sh
USER pyvmomi

# Default command
ENTRYPOINT ["/entrypoint.sh"]
CMD ["bash"]
