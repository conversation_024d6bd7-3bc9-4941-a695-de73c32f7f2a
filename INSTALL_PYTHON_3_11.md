# Python 3.11.9 安装和配置指南

本指南将帮助您在 Python 3.11.9 环境中安装和配置 pyvmomi-community-samples 项目。

## 前提条件

- Python 3.11.9 (推荐) 或 Python 3.8+
- pip (Python 包管理器)
- 网络连接 (用于下载依赖项)

## 安装步骤

### 1. 验证 Python 版本

```bash
python --version
```

应该显示 Python 3.11.9 或更高版本。

### 2. 创建虚拟环境 (推荐)

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate
```

### 3. 升级 pip

```bash
python -m pip install --upgrade pip
```

### 4. 安装项目依赖

```bash
# 安装主要依赖
pip install -r requirements.txt

# 安装测试依赖 (可选)
pip install -r test-requirements.txt
```

### 5. 验证安装

运行兼容性测试脚本：

```bash
python test_compatibility.py
```

如果看到 "🎉 所有兼容性测试通过!" 消息，说明安装成功。

## 依赖项说明

### 核心依赖项

- **pyvmomi (>=7.0.3)**: VMware vSphere API Python 绑定
- **requests (>=2.25.1)**: HTTP 库
- **vcrpy (>=4.1.1)**: HTTP 交互录制/回放
- **pyinstaller (>=4.5.1)**: 创建可执行文件

### SOAP 客户端库 (条件依赖)

根据 Python 版本自动选择：
- **Python 3.11+**: suds-community (>=1.1.0)
- **Python 3.0-3.10**: suds-jurko (>=0.6)
- **Python 2.x**: suds (>=0.4,<0.7) - 不再支持

### 测试依赖项

- **mock (>=4.0.3)**: 模拟对象库
- **testtools (>=2.5.0)**: 测试工具
- **pytest (>=6.2.5)**: 测试框架
- **pytest-cov (>=3.0.0)**: 测试覆盖率

## 常见问题和解决方案

### 问题 1: suds 相关错误

**错误信息:**
```
No module named 'suds'
```

**解决方案:**
确保使用了正确的 suds 版本。对于 Python 3.11，应该安装 `suds-community`：

```bash
pip install suds-community>=1.1.0
```

### 问题 2: pyvmomi 版本不兼容

**错误信息:**
```
ImportError: cannot import name 'xxx' from 'pyVmomi'
```

**解决方案:**
更新到支持 Python 3.11 的 pyvmomi 版本：

```bash
pip install --upgrade pyvmomi>=7.0.3
```

### 问题 3: SSL 证书错误

**错误信息:**
```
SSL: CERTIFICATE_VERIFY_FAILED
```

**解决方案:**
这通常是由于 vCenter 使用自签名证书。在示例代码中，大多数脚本都有禁用 SSL 验证的选项。

### 问题 4: 编码问题

**错误信息:**
```
UnicodeDecodeError: 'ascii' codec can't decode byte
```

**解决方案:**
确保您的环境支持 UTF-8 编码。在 Windows 上，可以设置环境变量：

```bash
set PYTHONIOENCODING=utf-8
```

## 使用示例

### 基本连接测试

```python
from tools import cli, service_instance

# 创建参数解析器
parser = cli.Parser()
args = parser.get_args()

# 连接到 vCenter
si = service_instance.connect(args)
print(f"连接成功: {si.content.about.fullName}")

# 断开连接
si.content.sessionManager.Logout()
```

### 运行示例脚本

```bash
# 获取所有虚拟机信息
python samples/getallvms.py -s your-vcenter.com -u username -p password

# 获取数据中心信息
python samples/get_dc_summary_info.py -s your-vcenter.com -u username -p password
```

## 开发环境设置

### 1. 安装开发工具

```bash
pip install flake8 pylint black
```

### 2. 代码格式化

```bash
# 使用 black 格式化代码
black samples/

# 使用 flake8 检查代码风格
flake8 samples/
```

### 3. 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest samples/tests/

# 生成覆盖率报告
pytest --cov=samples
```

## 性能优化建议

1. **使用虚拟环境**: 避免依赖项冲突
2. **缓存连接**: 重用 ServiceInstance 对象
3. **批量操作**: 使用 PropertyCollector 进行批量查询
4. **异步处理**: 对于长时间运行的任务，使用异步模式

## 故障排除

### 启用调试日志

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 检查网络连接

```bash
# 测试到 vCenter 的连接
telnet your-vcenter.com 443
```

### 验证凭据

确保使用的用户账户具有足够的权限访问 vSphere API。

## 获取帮助

如果遇到问题：

1. 查看 [PYTHON_3_11_COMPATIBILITY.md](PYTHON_3_11_COMPATIBILITY.md) 了解兼容性详情
2. 运行 `python test_compatibility.py` 检查环境
3. 查看项目的 GitHub Issues
4. 参考 VMware pyvmomi 官方文档

## 更新和维护

定期更新依赖项以获得最新的安全修复和功能：

```bash
pip list --outdated
pip install --upgrade pyvmomi requests
```
