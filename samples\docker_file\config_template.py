# -*- coding: utf-8 -*-
"""
Template configuration file for pyvmomi-community-samples Docker container
Copy this file to self_config.py and modify the values according to your environment
"""

import os

# vCenter connection settings
# These can be overridden by environment variables
host = os.getenv('VCENTER_HOST', '*************')
user = os.getenv('VCENTER_USER', '<EMAIL>')
password = os.getenv('VCENTER_PASSWORD', 'your_password')
port = int(os.getenv('VCENTER_PORT', '443'))
disable_ssl_verification = os.getenv('DISABLE_SSL_VERIFY', 'true').lower() == 'true'

# Output settings
silent = os.getenv('SILENT', 'false').lower() == 'true'
out_put_json = os.getenv('OUTPUT_JSON', 'false').lower() == 'true'
jsonfile = os.getenv('JSON_FILE', 'vms.json')

# CMDB integration settings
add_to_cmdb = os.getenv('ADD_TO_CMDB', 'false').lower() == 'true'

# CMDB URLs (modify according to your CMDB system)
cmdb_host = os.getenv('CMDB_HOST', 'http://cmdb.example.com')
cmdb_add_inst_url = f"{cmdb_host}/api/create_inst"
cmdb_add_host_url = f"{cmdb_host}/api/add_host"
cmdb_add_inst_asst_url = f"{cmdb_host}/api/set_inst_asst"

# Cloud platform integration (optional)
cloudsino_host = os.getenv('CLOUDSINO_HOST', 'https://cloud.example.com')
cloudsino_get_device_url = f"{cloudsino_host}/api/getdeviceinfo"
cloudsino_token = os.getenv('CLOUDSINO_TOKEN', '')

# Monitoring settings (for falcon integration)
endpoint = os.getenv('FALCON_ENDPOINT', 'vcenter')
push_api = os.getenv('FALCON_PUSH_API', 'http://127.0.0.1:1988/v1/push')
push_api2 = os.getenv('FALCON_PUSH_API2', 'http://127.0.0.1:1988/v1/push')
interval = int(os.getenv('FALCON_INTERVAL', '60'))

# Collection filters
esxi_names = []  # Leave empty to collect all ESXi hosts
datastore_names = []  # Leave empty to collect all datastores
vm_enable = True  # Whether to collect VM information
vm_names = []  # Leave empty to collect all VMs

# Example usage in Docker:
# docker run -e VCENTER_HOST=vcenter.example.com \
#            -e VCENTER_USER=admin \
#            -e VCENTER_PASSWORD=password \
#            -e OUTPUT_JSON=true \
#            pyvmomi-samples python your_script.py
