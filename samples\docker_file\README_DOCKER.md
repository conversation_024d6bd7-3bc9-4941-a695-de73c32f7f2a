# PyVmomi Community Samples Docker 版本

这个 Docker 版本为 pyvmomi-community-samples 项目提供了一个完整的容器化环境，包含了所有必要的依赖和 Python 3.11 环境。

## 快速开始

### 1. 构建 Docker 镜像

```bash
# 在项目根目录下执行
cd samples/docker_file
docker build -t pyvmomi-samples -f Dockerfile ../..
```

### 2. 使用 Docker Compose（推荐）

```bash
# 启动容器
docker-compose up -d

# 进入交互式 shell
docker-compose exec pyvmomi-samples bash

# 查看日志
docker-compose logs pyvmomi-samples
```

### 3. 直接运行 Docker 容器

```bash
# 交互式运行
docker run -it pyvmomi-samples bash

# 运行特定脚本
docker run -e VCENTER_HOST=vcenter.example.com \
           -e VCENTER_USER=admin \
           -e VCENTER_PASSWORD=password \
           pyvmomi-samples hello
```

## 环境变量配置

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `VCENTER_HOST` | vCenter 服务器地址 | 无 |
| `VCENTER_USER` | vCenter 用户名 | 无 |
| `VCENTER_PASSWORD` | vCenter 密码 | 无 |
| `VCENTER_PORT` | vCenter 端口 | 443 |
| `DISABLE_SSL_VERIFY` | 禁用 SSL 验证 | true |
| `OUTPUT_JSON` | 输出 JSON 格式 | false |
| `SILENT` | 静默模式 | false |

## 可用命令

容器支持以下预定义命令：

- `hello` - 运行 hello_world_vcenter.py 示例
- `list-vms` - 运行 getallvms.py 示例
- `python <script.py>` - 运行指定的 Python 脚本
- `bash` - 启动交互式 shell
- `help` - 显示帮助信息

## 使用示例

### 基本连接测试

```bash
docker run -e VCENTER_HOST=************* \
           -e VCENTER_USER=<EMAIL> \
           -e VCENTER_PASSWORD=your_password \
           pyvmomi-samples hello
```

### 列出所有虚拟机

```bash
docker run -e VCENTER_HOST=************* \
           -e VCENTER_USER=<EMAIL> \
           -e VCENTER_PASSWORD=your_password \
           pyvmomi-samples list-vms
```

### 运行自定义脚本

```bash
# 挂载本地脚本到容器
docker run -v /path/to/your/script.py:/app/samples/custom_script.py \
           -e VCENTER_HOST=************* \
           -e VCENTER_USER=admin \
           -e VCENTER_PASSWORD=password \
           pyvmomi-samples python custom_script.py
```

### 交互式开发

```bash
# 启动开发环境
docker-compose --profile dev up -d pyvmomi-dev

# 进入开发容器
docker-compose exec pyvmomi-dev bash
```

## 文件挂载

### 配置文件挂载

```bash
# 挂载自定义配置文件
docker run -v /path/to/your/self_config.py:/app/samples/self_config.py \
           pyvmomi-samples python your_script.py
```

### 输出目录挂载

```bash
# 挂载输出目录
docker run -v /path/to/output:/app/output \
           -e OUTPUT_JSON=true \
           pyvmomi-samples python script_that_generates_output.py
```

## 故障排除

### 1. SSL 证书问题

如果遇到 SSL 证书验证问题，确保设置了 `DISABLE_SSL_VERIFY=true`：

```bash
docker run -e DISABLE_SSL_VERIFY=true \
           -e VCENTER_HOST=your_vcenter \
           pyvmomi-samples hello
```

### 2. 网络连接问题

确保容器能够访问 vCenter 服务器：

```bash
# 测试网络连接
docker run pyvmomi-samples bash -c "ping your_vcenter_host"
```

### 3. 权限问题

容器以非 root 用户运行，如果需要特殊权限：

```bash
# 以 root 用户运行
docker run --user root pyvmomi-samples bash
```

## 开发和调试

### 查看容器内部

```bash
# 进入运行中的容器
docker exec -it pyvmomi-samples bash

# 查看 Python 环境
docker exec pyvmomi-samples python --version
docker exec pyvmomi-samples pip list
```

### 日志查看

```bash
# 查看容器日志
docker logs pyvmomi-samples

# 实时查看日志
docker logs -f pyvmomi-samples
```

## 安全注意事项

1. **不要在镜像中硬编码密码**
2. **使用环境变量或挂载配置文件传递敏感信息**
3. **在生产环境中使用具体的镜像标签而不是 latest**
4. **定期更新基础镜像以获取安全补丁**

## 构建选项

### 自定义构建

```bash
# 使用不同的 Python 版本
docker build --build-arg PYTHON_VERSION=3.10 -t pyvmomi-samples:py310 .

# 添加额外的包
docker build --build-arg EXTRA_PACKAGES="vim curl wget" -t pyvmomi-samples:extended .
```

### 多阶段构建（生产优化）

如需要更小的生产镜像，可以修改 Dockerfile 使用多阶段构建。

## 支持和贡献

如果遇到问题或有改进建议，请：

1. 检查现有的 GitHub Issues
2. 创建新的 Issue 描述问题
3. 提交 Pull Request 贡献代码

## 许可证

本 Docker 配置遵循与主项目相同的 Apache 2.0 许可证。
