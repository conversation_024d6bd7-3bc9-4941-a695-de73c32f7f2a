[![Build Status](https://travis-ci.org/vmware/pyvmomi-community-samples.svg?branch=master)](https://travis-ci.org/vmware/pyvmomi-community-samples) 

pyvmomi-community-samples
=========================

> Community contributed samples for the pyVmomi library.

The pyVmomi library itself is hosted here:
    https://github.com/vmware/pyvmomi/

The community samples website is here:
    http://vmware.github.io/pyvmomi-community-samples/

The samples contained here in are provided as-is from contributors around the
pyVmomi community. They are intended as learning tools and may vary in quality.
Any developer may contribute, enhance, or alter the samples. Ownership of any
submitted sample reverts to the project itself

# Philosophy

* Sample Ownership and Copyright Licenses
  * the community creates the samples and the project retains ownership of the samples
  * the community curates the samples and licenses the samples under the project license
    * all samples are distributed under the root project license
    * A copyright line may be added if you contribute a change of more than 10 lines

* Writing good samples:
    * A good sample shows how to do one thing and only one thing at a time.
    * A good sample follows good and consistent code style see: [pep8](http://legacy.python.org/dev/peps/pep-0008/).
    * When arbitrary choices are made, a good sample chooses the simplest default.
    * Samples must be willing to teach programming as well as the API!

# Code Style

Please conform to pep8 standards. Check your code by running the pycodestyle tool.
    https://pypi.python.org/pypi/pycodestyle

# Contribution Process

* Follow the [GitHub process](https://help.github.com/articles/fork-a-repo)
  * please use one branch per sample or change-set
  * please use one commit and pull request per sample
  * if you include a license with your sample, use the project license
