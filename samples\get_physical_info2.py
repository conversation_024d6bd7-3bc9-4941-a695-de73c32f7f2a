#!/usr/bin/env python
#
# Written by <PERSON><PERSON>
# GitHub: https://github.com/jm66
# Email: <EMAIL>
# Website: http://jose-manuel.me
#
# Note: Example code For testing purposes only
#
# This code has been released under the terms of the Apache-2.0 license
# http://opensource.org/licenses/Apache-2.0
#

import requests
import json
from pyVmomi import vim
from tools import cli, service_instance

# disable  urllib3 warnings
requests.packages.urllib3.disable_warnings(
    requests.packages.urllib3.exceptions.InsecureRequestWarning)


def main():
    parser = cli.Parser()
    parser.add_optional_arguments(cli.Argument.DATASTORE_NAME)
    args = parser.get_args()
    si = service_instance.connect(args)
    content = si.RetrieveContent()

    container = content.viewManager.CreateContainerView(content.rootFolder, [vim.HostSystem], True)
    esxi_obj = [view for view in container.view]
    esxi_arr_total = []
    for esxi in esxi_obj:
        esxi_arr = []
        esxi_host_summary_info = {
            "CPU": esxi.summary.hardware.cpuModel,
            "CPUNum": esxi.summary.hardware.numCpuPkgs,
            "CPUNumberOfCores": esxi.summary.hardware.numCpuCores,
            "CPUThreadNum": esxi.summary.hardware.numCpuThreads,
            "memory": int(esxi.summary.hardware.memorySize / 1024 ** 3),
            "memUsed": int(esxi.summary.quickStats.overallMemoryUsage / 1024 if esxi.summary.quickStats.overallMemoryUsage else 0),
            "memFree": int(esxi.summary.hardware.memorySize / 1024 ** 3 - (esxi.summary.quickStats.overallMemoryUsage / 1024 if esxi.summary.quickStats.overallMemoryUsage else 0)),
            "hostname": esxi.name,
            "os": esxi.summary.config.product.fullName,
            "osType": "Esxi",
            "osBit": "64",
            "serialNumber": "",
            "diskSize": "",
            "computermodel": esxi.summary.hardware.model
        }
        for i in esxi.summary.hardware.otherIdentifyingInfo:
            if isinstance(i, vim.host.SystemIdentificationInfo) and i.identifierType.key == "SerialNumberTag":
                esxi_host_summary_info["serialNumber"] = i.identifierValue
        disk_size = 0
        for ds in esxi.datastore:
            disk_size += int(ds.summary.capacity / 1024 ** 3)
        esxi_host_summary_info["diskSize"] = disk_size
        esxi_arr.append(esxi_host_summary_info)
        esxi_arr_total.append(esxi_arr)
    print "%s" % json.dumps(esxi_arr_total)


# start
if __name__ == "__main__":
    main()