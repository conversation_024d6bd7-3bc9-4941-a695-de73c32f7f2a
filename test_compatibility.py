#!/usr/bin/env python
"""
Python 3.11.9 兼容性测试脚本

此脚本测试项目的 Python 3.11.9 兼容性，包括：
1. 导入测试
2. 基本功能测试
3. 兼容性代码测试
"""

import sys
import importlib
import traceback

def test_python_version():
    """测试 Python 版本"""
    print(f"Python 版本: {sys.version}")
    version_info = sys.version_info
    
    if version_info.major < 3:
        print("❌ 错误: 需要 Python 3.x")
        return False
    elif version_info.major == 3 and version_info.minor < 8:
        print("❌ 错误: 需要 Python 3.8 或更高版本")
        return False
    else:
        print("✅ Python 版本兼容")
        return True

def test_imports():
    """测试关键导入"""
    imports_to_test = [
        ('urllib.request', 'urllib.request'),
        ('urllib.parse', 'urllib.parse'),
        ('base64', 'base64'),
        ('http.cookiejar', 'http.cookiejar'),
    ]
    
    optional_imports = [
        ('pyvmomi', 'pyvmomi'),
        ('suds', 'suds'),
        ('requests', 'requests'),
    ]
    
    print("\n测试必需导入:")
    all_passed = True
    
    for module_name, import_name in imports_to_test:
        try:
            importlib.import_module(module_name)
            print(f"✅ {import_name} 导入成功")
        except ImportError as e:
            print(f"❌ {import_name} 导入失败: {e}")
            all_passed = False
    
    print("\n测试可选导入:")
    for module_name, import_name in optional_imports:
        try:
            importlib.import_module(module_name)
            print(f"✅ {import_name} 导入成功")
        except ImportError as e:
            print(f"⚠️  {import_name} 导入失败 (可选): {e}")
    
    return all_passed

def test_cookielib_compatibility():
    """测试 cookielib 兼容性代码"""
    print("\n测试 cookielib 兼容性:")
    try:
        # 模拟 pyvmomi-to-suds.py 中的导入逻辑
        try:
            import cookielib
            print("⚠️  使用 Python 2 风格的 cookielib (不推荐)")
        except ImportError:
            import http.cookiejar as cookielib
            print("✅ 使用 Python 3 风格的 http.cookiejar")
        
        # 测试基本功能
        jar = cookielib.CookieJar()
        print("✅ CookieJar 创建成功")
        return True
    except Exception as e:
        print(f"❌ cookielib 兼容性测试失败: {e}")
        return False

def test_unicode_compatibility():
    """测试 unicode 兼容性代码"""
    print("\n测试 unicode 兼容性:")
    try:
        # 模拟 getorphanedvms.py 中的逻辑
        test_string = "test string"
        
        try:
            # Python 2 风格检查
            if isinstance(test_string, unicode):
                test_string = test_string.encode('utf-8', 'ignore')
            print("⚠️  检测到 unicode 类型 (Python 2)")
        except NameError:
            # Python 3: unicode 不存在
            print("✅ Python 3 兼容: unicode 类型不存在")
        
        return True
    except Exception as e:
        print(f"❌ unicode 兼容性测试失败: {e}")
        return False

def test_base64_compatibility():
    """测试 base64 兼容性代码"""
    print("\n测试 base64 兼容性:")
    try:
        import base64
        
        test_data = "test:password"
        
        # 模拟 getorphanedvms.py 中的逻辑
        try:
            # Python 3 方式
            encoded = base64.encodebytes(test_data.encode('utf-8')).decode('ascii').replace('\n', '')
            print("✅ 使用 base64.encodebytes (Python 3)")
        except AttributeError:
            # Python 2 回退
            encoded = base64.encodestring(test_data).replace('\n', '')
            print("⚠️  使用 base64.encodestring (Python 2 回退)")
        
        # 验证编码结果
        if encoded:
            print(f"✅ base64 编码成功: {encoded[:20]}...")
            return True
        else:
            print("❌ base64 编码结果为空")
            return False
            
    except Exception as e:
        print(f"❌ base64 兼容性测试失败: {e}")
        return False

def test_string_formatting():
    """测试字符串格式化兼容性"""
    print("\n测试字符串格式化:")
    try:
        # 测试 % 格式化 (项目中使用的)
        host = "example.com"
        url = "https://%s/sdk/vimService.wsdl" % host
        print(f"✅ % 格式化: {url}")
        
        # 测试 .format() 格式化
        name = "test"
        formatted = "Found: {0}".format(name)
        print(f"✅ .format() 格式化: {formatted}")
        
        # 测试 f-string (Python 3.6+)
        if sys.version_info >= (3, 6):
            f_formatted = f"Python {sys.version_info.major}.{sys.version_info.minor}"
            print(f"✅ f-string 格式化: {f_formatted}")
        
        return True
    except Exception as e:
        print(f"❌ 字符串格式化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("Python 3.11.9 兼容性测试")
    print("=" * 60)
    
    tests = [
        ("Python 版本", test_python_version),
        ("导入测试", test_imports),
        ("cookielib 兼容性", test_cookielib_compatibility),
        ("unicode 兼容性", test_unicode_compatibility),
        ("base64 兼容性", test_base64_compatibility),
        ("字符串格式化", test_string_formatting),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有兼容性测试通过!")
        return 0
    else:
        print("⚠️  部分测试失败，请检查兼容性问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
