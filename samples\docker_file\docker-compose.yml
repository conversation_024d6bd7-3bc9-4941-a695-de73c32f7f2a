version: '3.8'

services:
  pyvmomi-samples:
    build:
      context: ../..
      dockerfile: samples/docker_file/Dockerfile
    image: pyvmomi-samples:latest
    container_name: pyvmomi-samples
    environment:
      # vCenter connection settings
      # Uncomment and set these values or pass them at runtime
      # VCENTER_HOST: "vcenter.example.com"
      # VCENTER_USER: "<EMAIL>"
      # VCENTER_PASSWORD: "your_password"
      # VCENTER_PORT: "443"
      # DISABLE_SSL_VERIFY: "true"
      
      # Python settings
      PYTHONUNBUFFERED: "1"
      PYTHONDONTWRITEBYTECODE: "1"
    
    # Mount volumes for custom scripts or configuration
    volumes:
      # Mount custom scripts directory
      # - ./custom_scripts:/app/samples/custom_scripts
      
      # Mount configuration files
      # - ./config:/app/config
      
      # Mount output directory for results
      # - ./output:/app/output
      
      # Example: Mount a custom self_config.py
      # - ./self_config.py:/app/samples/self_config.py
    
    # Network configuration
    networks:
      - pyvmomi-network
    
    # Keep container running for interactive use
    stdin_open: true
    tty: true
    
    # Override default command if needed
    # command: ["python", "hello_world_vcenter.py"]

  # Optional: Add a development service with additional tools
  pyvmomi-dev:
    build:
      context: ../..
      dockerfile: samples/docker_file/Dockerfile
    image: pyvmomi-samples:latest
    container_name: pyvmomi-dev
    environment:
      PYTHONUNBUFFERED: "1"
      PYTHONDONTWRITEBYTECODE: "1"
    volumes:
      # Mount the entire project for development
      - ../..:/app
      # Mount custom scripts
      - ./dev_scripts:/app/samples/dev_scripts
    networks:
      - pyvmomi-network
    stdin_open: true
    tty: true
    command: ["bash"]
    profiles:
      - dev

networks:
  pyvmomi-network:
    driver: bridge
